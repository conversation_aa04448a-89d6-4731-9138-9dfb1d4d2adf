#!/usr/bin/env python3
"""
API配置功能测试脚本
测试不同API前缀配置下的接口访问
"""

import os
import sys
import requests
import json
import time

def test_api_prefix(base_url, api_prefix):
    """测试指定API前缀的接口访问"""
    print(f"\n=== 测试API前缀: {api_prefix} ===")
    
    # 测试版本接口
    version_url = f"{base_url}{api_prefix}/stat/version"
    print(f"测试URL: {version_url}")
    
    try:
        response = requests.get(version_url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ 失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        return False

def test_api_config_endpoint(base_url, api_prefix):
    """测试API配置管理接口"""
    print(f"\n=== 测试API配置管理接口 ===")
    
    # 获取当前配置
    config_url = f"{base_url}{api_prefix}/api-config/get"
    print(f"获取配置URL: {config_url}")
    
    try:
        response = requests.get(config_url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取配置成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 测试设置配置
            set_url = f"{base_url}{api_prefix}/api-config/set"
            test_config = {
                "api_prefix": "/test/api"
            }
            
            set_response = requests.post(set_url, json=test_config, timeout=5)
            if set_response.status_code == 200:
                set_data = set_response.json()
                print(f"✅ 设置配置成功: {json.dumps(set_data, indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 设置配置失败: HTTP {set_response.status_code}")
                return False
        else:
            print(f"❌ 获取配置失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API配置功能测试")
    
    # 配置
    base_url = "http://localhost:6185"
    test_prefixes = [
        "/api",
        "/chat/api", 
        "/v1/api",
        "/bot/api"
    ]
    
    print(f"测试服务器: {base_url}")
    print(f"测试前缀: {test_prefixes}")
    
    # 测试每个前缀
    results = {}
    for prefix in test_prefixes:
        results[prefix] = test_api_prefix(base_url, prefix)
        time.sleep(1)  # 避免请求过快
    
    # 测试API配置管理接口（使用默认前缀）
    config_result = test_api_config_endpoint(base_url, "/api")
    
    # 输出测试结果
    print(f"\n{'='*50}")
    print("📊 测试结果汇总:")
    print(f"{'='*50}")
    
    for prefix, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{prefix:<15} {status}")
    
    config_status = "✅ 通过" if config_result else "❌ 失败"
    print(f"{'配置管理':<15} {config_status}")
    
    # 总体结果
    total_tests = len(results) + 1
    passed_tests = sum(results.values()) + (1 if config_result else 0)
    
    print(f"\n总计: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
