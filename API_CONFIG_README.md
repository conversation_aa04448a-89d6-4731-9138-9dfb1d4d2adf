# API接口URL配置说明

本次改造实现了API接口URL的统一配置功能，支持前端和后端的统一管理。

## 功能特性

### 1. 前端统一API封装
- 创建了 `dashboard/src/utils/api.ts` 统一API客户端
- 支持可配置的API基础URL
- 自动处理认证token
- 统一错误处理
- 配置持久化到localStorage

### 2. 后端可配置API前缀
- 支持通过环境变量 `API_PREFIX` 配置
- 支持通过配置文件 `dashboard.api_prefix` 配置
- 新增API配置管理路由 `/api/api-config/`

### 3. 用户界面配置
- 在顶部导航栏添加API配置按钮
- 可视化配置界面，支持实时测试
- 配置验证和错误提示

## 使用方法

### 环境变量配置
```bash
# 设置API前缀
export API_PREFIX="/chat/api"

# 启动应用
python main.py
```

### 配置文件配置
在 `data/cmd_config.json` 中添加：
```json
{
  "dashboard": {
    "api_prefix": "/chat/api"
  }
}
```

### 前端界面配置
1. 点击顶部导航栏的齿轮图标
2. 在弹出的对话框中输入新的API前缀
3. 点击保存，系统会自动测试连接
4. 配置成功后会保存到本地和服务器

## 支持的API前缀格式

- `/api` (默认)
- `/chat/api`
- `/v1/api`
- `/bot/api`
- `http://localhost:8080/api` (完整URL)

## 配置优先级

1. 环境变量 `API_PREFIX`
2. 配置文件 `dashboard.api_prefix`
3. 默认值 `/api`

## 技术实现

### 前端改造
1. **API客户端封装** (`dashboard/src/utils/api.ts`)
   - 基于axios的统一封装
   - 支持动态baseURL配置
   - 自动token管理
   - 响应拦截和错误处理

2. **配置组件** (`dashboard/src/components/ApiConfigDialog.vue`)
   - Vue 3 Composition API
   - 表单验证
   - 实时连接测试
   - 配置持久化

3. **API调用迁移**
   - 将直接的axios调用替换为统一的api客户端
   - 路径从绝对路径改为相对路径
   - 保持原有功能不变

### 后端改造
1. **路由基类改造** (`astrbot/dashboard/routes/route.py`)
   - 添加 `get_api_prefix()` 方法
   - 支持从环境变量和配置文件读取
   - 动态路由注册

2. **服务器改造** (`astrbot/dashboard/server.py`)
   - 认证中间件支持动态前缀
   - 插件路由支持动态前缀
   - 配置读取逻辑

3. **配置管理路由** (`astrbot/dashboard/routes/api_config.py`)
   - GET `/api/api-config/get` - 获取当前配置
   - POST `/api/api-config/set` - 设置新配置
   - 配置验证和持久化

## 兼容性

- 向后兼容：默认使用 `/api` 前缀
- 渐进式迁移：可以逐步替换axios调用
- 配置灵活：支持多种配置方式

## 注意事项

1. 修改API前缀后需要重启后端服务才能完全生效
2. 前端配置会立即生效，但建议同时更新后端配置
3. 使用完整URL时需要注意CORS配置
4. 配置验证确保前缀格式正确

## 测试验证

### 1. 后端测试脚本
提供了测试脚本 `test_api_config.py` 来验证API配置功能：

```bash
# 运行测试脚本
python test_api_config.py
```

测试内容包括：
- 不同API前缀的接口访问测试
- API配置管理接口测试
- 配置持久化验证

### 2. 前端测试页面
访问 `http://localhost:3000/#/main/api-config-test` 进行前端测试：

- 查看当前API配置
- 测试API调用功能
- 使用API配置组件
- 手动更新配置

### 3. 错误修复
修复了国际化相关的错误：
- 正确注册了 `apiConfig` 翻译模块
- 添加了中英文翻译文件
- 修复了 `useI18n` 导入问题

## 示例场景

### 场景1：添加统一前缀
原接口：`/api/auth/login`
配置前缀：`/chat/api`
新接口：`/chat/api/auth/login`

### 场景2：版本化API
原接口：`/api/config/get`
配置前缀：`/v1/api`
新接口：`/v1/api/config/get`

### 场景3：跨域代理
原接口：`/api/stat/version`
配置前缀：`http://api.example.com/api`
新接口：`http://api.example.com/api/stat/version`

## 故障排除

### 常见问题

1. **前端配置后无法访问接口**
   - 检查vite代理配置是否包含新的API前缀
   - 确认后端服务已重启
   - 查看浏览器控制台错误信息

2. **后端配置不生效**
   - 确认配置文件格式正确
   - 检查环境变量是否正确设置
   - 重启后端服务

3. **流式接口（如聊天）无法工作**
   - 确认fetch调用使用了正确的API前缀
   - 检查CORS配置

### 调试方法

1. 查看浏览器开发者工具的网络面板
2. 检查后端日志输出
3. 使用测试脚本验证接口可用性
