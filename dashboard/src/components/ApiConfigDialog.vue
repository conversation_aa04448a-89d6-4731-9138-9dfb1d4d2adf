<template>
  <v-dialog v-model="dialog" max-width="500px" persistent>
    <template v-slot:activator="{ props }">
      <v-btn
        icon="mdi-cog"
        variant="text"
        size="small"
        v-bind="props"
        :title="t('core.apiConfig.title')"
      />
    </template>

    <v-card>
      <v-card-title class="text-h5">
        <v-icon class="mr-2">mdi-api</v-icon>
        {{ t('core.apiConfig.title') }}
      </v-card-title>

      <v-card-text>
        <v-form ref="form" v-model="valid">
          <v-text-field
            v-model="apiBaseURL"
            :label="t('core.apiConfig.baseURL')"
            :placeholder="t('core.apiConfig.baseURLPlaceholder')"
            :rules="urlRules"
            variant="outlined"
            density="comfortable"
            prepend-inner-icon="mdi-link"
            clearable
          >
            <template v-slot:append-inner>
              <v-btn
                icon="mdi-refresh"
                variant="text"
                size="small"
                @click="resetToDefault"
                :title="t('core.apiConfig.resetToDefault')"
              />
            </template>
          </v-text-field>

          <v-alert
            v-if="showWarning"
            type="warning"
            variant="tonal"
            class="mb-4"
          >
            {{ t('core.apiConfig.warning') }}
          </v-alert>

          <v-card variant="tonal" class="mb-4">
            <v-card-text>
              <div class="text-subtitle-2 mb-2">{{ t('core.apiConfig.examples') }}</div>
              <div class="text-body-2 text-medium-emphasis">
                <div>• /api ({{ t('core.apiConfig.default') }})</div>
                <div>• /chat/api</div>
                <div>• /v1/api</div>
                <div>• http://localhost:8080/api</div>
              </div>
            </v-card-text>
          </v-card>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn
          variant="text"
          @click="cancel"
        >
          {{ t('common.cancel') }}
        </v-btn>
        <v-btn
          color="primary"
          variant="flat"
          @click="save"
          :disabled="!valid"
          :loading="saving"
        >
          {{ t('common.save') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from '@/i18n/composables';
import apiClient from '@/utils/api';

const { t } = useI18n();

// 响应式数据
const dialog = ref(false);
const valid = ref(false);
const saving = ref(false);
const apiBaseURL = ref('/api');
const originalURL = ref('/api');

// 计算属性
const showWarning = computed(() => {
  return apiBaseURL.value !== '/api' && apiBaseURL.value !== originalURL.value;
});

// 验证规则
const urlRules = [
  (v: string) => !!v || t('core.apiConfig.validation.required'),
  (v: string) => {
    // 允许相对路径或完整URL
    const relativePathPattern = /^\/[\w\-\/]*$/;
    const urlPattern = /^https?:\/\/[\w\-\.]+(:\d+)?(\/[\w\-\/]*)?$/;
    return relativePathPattern.test(v) || urlPattern.test(v) || t('core.apiConfig.validation.invalid');
  },
];

// 监听对话框打开
watch(dialog, async (newVal) => {
  if (newVal) {
    try {
      // 从后端获取当前配置
      const response = await apiClient.get('/api-config/get');
      const serverConfig = response.data.data;
      apiBaseURL.value = serverConfig.api_prefix;
      originalURL.value = serverConfig.api_prefix;
    } catch (error) {
      // 如果后端获取失败，使用客户端配置
      const currentConfig = apiClient.getConfig();
      apiBaseURL.value = currentConfig.baseURL;
      originalURL.value = currentConfig.baseURL;
    }
  }
});

// 重置为默认值
const resetToDefault = () => {
  apiBaseURL.value = '/api';
};

// 取消
const cancel = () => {
  apiBaseURL.value = originalURL.value;
  dialog.value = false;
};

// 保存
const save = async () => {
  if (!valid.value) return;

  saving.value = true;
  try {
    // 更新API客户端配置
    apiClient.updateBaseURL(apiBaseURL.value);

    // 测试连接
    await apiClient.get('/stat/version');

    // 保存到后端配置
    try {
      await apiClient.post('/api-config/set', {
        api_prefix: apiBaseURL.value
      });
    } catch (backendError) {
      console.warn('保存到后端配置失败，但客户端配置已更新:', backendError);
    }

    originalURL.value = apiBaseURL.value;
    dialog.value = false;

    // 显示成功消息
    // 这里可以添加成功提示
  } catch (error) {
    // 恢复原始配置
    apiClient.updateBaseURL(originalURL.value);

    // 显示错误消息
    console.error('API配置测试失败:', error);
    // 这里可以添加错误提示
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.v-card-title {
  background: rgba(var(--v-theme-primary), 0.1);
}
</style>
